<html>
    <head>
        <meta id="viewport" name="viewport" content ="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <link rel="stylesheet" type="text/css" href="styles.css"/>
        <link rel="stylesheet" type="text/css" href="responsive.css"/>
        <link href="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.prod.css" rel="stylesheet" type="text/css"/>
        <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.13.0/css/all.css"/>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js" defer></script>
        <script src="https://cdn.jsdelivr.net/npm/quasar@2.1.0/dist/quasar.umd.prod.js" defer></script>
        <script src="app.js" defer></script>
    </head>
    <body>
        <div id="baseplate-container" v-show="show">
            <div v-if="showStreets" class="street-container">
                <div class="street2">{{street2}}</div>
                <div class="street1">{{street1}}</div>
            </div>
            <div class="baseplate" v-show="show">
                <div v-if="showPointer" class="pointer">˅</div>
                <div v-if="showDegrees" class="degrees"></div>
                    <svg class="bezel">
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="-90"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="-45"/>
                        <rect width="4.5" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="0"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="45"/>
                        <rect width="4.5" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="90"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="135"/>
                        <rect width="4.5" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="180"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="225"/>
                        <rect width="4.5" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="270"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="315"/>
                        <rect width="4.5" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="360"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="9" x="405"/>
                        <rect width="3" stroke="black" fill="white" stroke-width="0.5" stroke-opacity="0.6" v-if="showCompass" height="20" x="450"/>
                    </svg>

                    <svg class="bearing">
                        <text v-if="showCompass" x="0" y="1.5" dominant-baseline="middle" text-anchor="middle" fill= "yellow">N</text>
                        <text v-if="showCompass" x="360" y="1.5" dominant-baseline="middle" text-anchor="middle" fill= "yellow">N</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="315" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">NW</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="-45" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">NW</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="45" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">NE</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="405" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">NE</text>
                        <text v-if="showCompass" x="90" y="1.5" dominant-baseline="middle" text-anchor="middle" fill="white">E</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="135" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">SE</text>
                        <text v-if="showCompass" x="180" y="1.5" dominant-baseline="middle" text-anchor="middle" fill="white">S</text>
                        <text style="font-size: 1.4vh!important; font-weight: 800!important;" v-if="showCompass" x="225" y="-11" dominant-baseline="middle" text-anchor="middle" fill="white">SW</text>
                        <text v-if="showCompass" x="270" y="1.5" dominant-baseline="middle" text-anchor="middle" fill="white">W</text>
                    </svg>
                </div>
            </div>
        <div id="openmenu">
            <div id="menu">
                <q-splitter v-model="splitterModel" style="height: 50vh;">
                    <template v-slot:before>
                        <q-tabs v-model="tab" active-bg-color="active-tab" inline-label indicator-color="transparent" vertical class="text-tabcolor bg-panel">
                            <q-tab name="hud" icon="view_list" label="界面设置" style="height: 5.5vh; padding-left: 1vh; justify-content: end;"></q-tab>
                        </q-tabs>
                    </template>
                    <template v-slot:after>
                        <q-tab-panels v-model="tab" animated swipeable vertical transition-prev="jump-up" transition-next="jump-up" class="bg-active-tab">
                            <q-tab-panel name="hud">
                                <div class="q-mb-md">
                                    <div class="text-h6 q-mb-md">重置界面 <img class="brand-logo" align="right" src="./brand-logo.svg"></img></div>
                                    <div class="q-pa-md q-gutter-sm">
                                        <q-btn v-on:click="resetStorage($event)" :loading="progress[0].loading" :percentage="progress[0].percentage" text-color="textbutton" color="bgbutton" @click="startComputing(0)" style="width: 150px"> 重置设置
                                        <template v-slot:loading> <q-spinner-gears class="on-left"></q-spinner-gears> 重置中... </template></q-btn>
                                        <div class="text-h7 q-mb-md">如果您想将设置重置为默认值，请点击这个按钮！<br>（您需要重新登录才能成功重置菜单更改）</div>
                                        <q-btn v-on:click="restartHud($event)" :loading="progress[1].loading" :percentage="progress[1].percentage" text-color="textbutton" color="bgbutton" @click="startComputing(1)" style="width: 150px"> 重启界面
                                        <template v-slot:loading> <q-spinner-gears class="on-left"></q-spinner-gears> 重启中... </template></q-btn>
                                        <div class="text-h7 q-mb-md">如果您的界面出现问题，请重启它！或者您可以使用 /resethud 命令</div>
                                    </div>
                                    <hr>
                                    <div class="text-h6 q-mb-md">选项</div>
                                    <div class="text-h7">
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isOutMapChecked" v-on:click="showOutMap($event)" color="checkbox" val="1"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>仅在载具中显示小地图</q-item-label>
                                                <q-item-label class="text-h7" caption>禁用此选项将始终在屏幕上显示小地图</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isOutCompassChecked" v-on:click="showOutCompass($event)" color="checkbox" val="2"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>仅在载具中显示指南针</q-item-label>
                                                <q-item-label class="text-h7" caption>禁用此选项将始终在屏幕上显示指南针</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                        <q-item borderless tag="label" v-ripple>
                                            <q-item-section avatar>
                                                <q-checkbox v-model="isCompassFollowChecked" v-on:click="showFollowCompass($event)" color="checkbox" val="3"></q-checkbox>
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>指南针跟随镜头</q-item-label>
                                                <q-item-label class="text-h7" caption>禁用此选项将使您无法再使用鼠标旋转指南针</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                    </div>
                                    <hr>
                                    <div class="text-h6 q-mb-md">通知</div>
                                    <div class="text-h7">
                                        <q-checkbox v-on:click="openMenuSounds($event)" label='启用菜单音效' v-model="isOpenMenuSoundsChecked" color="checkbox" val="4" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="resetHudSounds($event)" label='启用重置界面音效' v-model="isResetSoundsChecked" color="checkbox" val="5" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="checklistSounds($event)" label='启用界面音效' v-model="isListSoundsChecked" color="checkbox" val="6" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showMapNotif($event)" label='启用地图通知' v-model="isMapNotifChecked" color="checkbox" val="7" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showFuelAlert($event)" label='启用低燃油警报' v-model="isLowFuelChecked" color="checkbox" val="8" style="display: flex;"></q-checkbox>
                                        <q-checkbox v-on:click="showCinematicNotif($event)" label='启用电影模式通知' v-model="isCinematicNotifChecked" color="checkbox" val="9" style="display: flex;"></q-checkbox>
                                    </div>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">状态</div>
                                <div class="text-h7">
                                    <q-checkbox v-on:click="dynamicHealth($event)" label='始终显示生命值' v-model="isDynamicHealthChecked" color="checkbox" val="10" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicArmor($event)" label='始终显示护甲值' v-model="isDynamicArmorChecked" color="checkbox" val="11" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicHunger($event)" label='始终显示饥饿值' v-model="isDynamicHungerChecked" color="checkbox" val="12" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicThirst($event)" label='始终显示口渴值' v-model="isDynamicThirstChecked" color="checkbox" val="13" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicStress($event)" label='始终显示压力值' v-model="isDynamicStressChecked" color="checkbox" val="14" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicOxygen($event)" label='始终显示氧气值' v-model="isDynamicOxygenChecked" color="checkbox" val="15" style="display: flex;"></q-checkbox>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">载具</div>
                                <div class="text-h7">
                                    <q-toggle v-on:click="changeFPS($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`速度表帧率 ${isChangeFPSChecked}`" unchecked-icon="60fps" false-value="同步" true-value="优化" checked-icon="30fps" v-model="isChangeFPSChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">同步帧率选项将减少优化，但保持速度表实时显示，但也会对您的机器要求更高。</div>
                                    <q-toggle v-on:click="ToggleMapShape($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`小地图 ${isToggleMapShapeChecked}`" unchecked-icon="radio_button_unchecked" false-value="圆形" true-value="方形" checked-icon="check_box_outline_blank" v-model="isToggleMapShapeChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">无论您想要方形还是圆形，您都可以选择！</div>
                                    <q-checkbox v-on:click="HideMap($event)" label='启用小地图' v-model="isHideMapChecked" color="checkbox" val="15" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="ToggleMapBorders($event)" label='启用小地图边框' v-model="isToggleMapBordersChecked" color="checkbox" val="16" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicEngine($event)" label='始终显示引擎状态' v-model="isDynamicEngineChecked" color="checkbox" val="17" style="display: flex;"></q-checkbox>
                                    <q-checkbox v-on:click="dynamicNitro($event)" label='始终显示氮气状态' v-model="isDynamicNitroChecked" color="checkbox" val="18" style="display: flex;"></q-checkbox>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">指南针</div>
                                <div class="text-h7">
                                    <q-toggle v-on:click="changeCompassFPS($event)" size="lg" keep-color icon-color="toggleicons" color="checkbox" :label="`指南针帧率 ${isChangeCompassFPSChecked}`" unchecked-icon="60fps" false-value="同步" true-value="优化" checked-icon="30fps" v-model="isChangeCompassFPSChecked"></q-toggle>
                                    <div class="text-h7 q-mb-md-d">同步帧率选项将减少优化，但保持指南针实时显示，但也会对您的机器要求更高。</div>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showCompassBase($event)" v-model="isShowCompassChecked" color="checkbox" val="19"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section >
                                            <q-item-label>启用指南针</q-item-label>
                                            <q-item-label class="text-h7" caption>禁用此选项将使您无法看到指南针导航</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showStreetsNames($event)" v-model="isShowStreetsChecked" color="checkbox" val="20"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Street Names Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see street names / locations</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showPointerIndex($event)" v-model="isPointerShowChecked" color="checkbox" val="21"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Compass Pointer Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see your pointer index to pinpoint your exact cardinal directions</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                    <q-item borderless tag="label" v-ripple>
                                        <q-item-section avatar>
                                            <q-checkbox v-on:click="showDegreesNum($event)" v-model="isDegreesShowChecked" color="checkbox" val="22"></q-checkbox>
                                        </q-item-section>
                                        <q-item-section>
                                            <q-item-label>Show Compass Degrees Enabled</q-item-label>
                                            <q-item-label class="text-h7" caption>Disabling this will make it so you can't see your exact degrees</q-item-label>
                                        </q-item-section>
                                    </q-item>
                                </div>
                                <hr>
                                <div class="text-h6 q-mb-md">Cinematic Mode</div>
                                <div class="text-h7">
                                    <q-checkbox v-on:click="cinematicMode($event)" label='Enabled' v-model="isCinematicModeChecked" color="checkbox" val="23" style="display: flex;"></q-checkbox>
                                </div>
                                <br>
                            </q-tab-panel>
                        </q-tab-panels>
                    </template>
                </q-splitter>
            </div>
        </div>
        <div id="main-container">
            <div id="money-container">
                <div id="money-cash">
                    <transition name="slide-fade">
                        <p v-if="showCash"><span id="sign">$&nbsp;</span><span id="money">{{(cash)}}</span></p>
                    </transition>
                </div>
                <div id="money-bank">
                    <transition name="slide-fade">
                        <p v-if="showBank"><span id="sign">$&nbsp;</span><span id="bank">{{(bank)}}</span></p>
                    </transition>
                </div>
                <div id="money-change" v-if="showUpdate">
                    <p v-if="plus" id="money"><span id="plus">+&nbsp;</span><span id="money">{{(amount)}}</span></p>
                    <p v-else-if="minus" id="minus"><span id="minus">-&nbsp;</span><span id="money">{{(amount)}}</span></p>
                </div>
            </div>
            <div id="ui-container">
                <div id="playerHud" v-show="show">
                    <transition name="fade">
                    <div v-if="showVoice">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: talkingColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="voice" :thickness="0.24" :style="{color: talkingColor}" :min="0" :max="5" center-color="grey-10">
                        <q-icon id="Icons" style="top: -0.5px; left: 0px;" :name="voiceIcon" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showHealth">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: healthColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="health" :thickness="0.24" :style="{color: healthColor}" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -0.5px; left: -0.4px;" name="fas fa-heart" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showArmor">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: armorColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="armor" :thickness="0.24" :style="{color: armorColor}" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-shield-alt" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showHunger">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: hungerColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="hunger" :thickness="0.24" :style="{color: hungerColor}" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -1px; left: -0.5px;" name="fas fa-hamburger" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showThirst">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: thirstColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="thirst" :thickness="0.24" :style="{color: thirstColor}" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -0.5px; left: -0.5px;" name="fas fa-tint" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showStress">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="stress" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="stress" :thickness="0.24" color="stress" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -0.5px; left: -0.5px;" name="fas fa-brain" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showOxygen">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="oxygen" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="oxygen" :thickness="0.24" color="oxygen" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -2px; left: -0.1px;" name="fas fa-lungs" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showArmed">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="armed" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="static" :thickness="0.24" color="armed" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-stream" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showParachute">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="parachute" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="parachute" :thickness="0.24" color="parachute" :min="0" :max="2" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-parachute-box" size="23px" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showEngine">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" :style="{color: engineColor}" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="engine" :thickness="0.24" :style="{color: engineColor}" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -1px;" name="fas fa-oil-can" size="19.5px":style="{color: engineColor}"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showHarness">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="harness" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="hp" :thickness="0.24" color="harness" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-user-slash" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showCruise">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="cruise" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="speed" :thickness="0.24" color="cruise" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" style="top: -1.5px;" name="fas fa-tachometer-alt-fast" color="white"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showNos">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="nos" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="nos" :thickness="0.24" color="nos" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-meteor" :style="{color: nosColor}"/>
                    </div>
                    </transition>
                    <transition name="fade">
                    <div v-if="showDev">
                        <q-circular-progress class="q-ml-xl" style="opacity: 40%;" :value="static" :thickness="0.24" color="dev" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress class="q-ml-xl" style="left: -50%;" show-value :value="static" :thickness="0.24" color="dev" :min="0" :max="100" center-color="grey-10">
                        <q-icon id="Icons" name="fas fa-terminal" color="white"/>
                    </div>
                    </transition>
                </div>
            </div>
            <div id="veh-container">
                <div v-show="show">
                    <div class="responsive" id="speedometer">
                        <q-circular-progress id="Speedo" class="q-ml-xl" style="transform: rotate(-150deg); opacity: 60%;" :value="speedometer" :thickness="0.21" color="gauge" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress id="Speedo" class="q-ml-xl" style="transform: rotate(-150deg); left: -50%;" show-value :value="speed" :thickness="0.21" color="gauge" :min="0" :max="600">
                        <speed id="Speed">{{(speed)}}</speed>
                    </div>
                    <div class="responsive" id="fuelgauge">
                        <q-circular-progress id="FuelGaugeBackground" class="q-ml-xl" style="transform: rotate(-125deg); opacity: 60%;" :value="fuelgauge" :thickness="0.21" color="gauge" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress id="FuelGaugeValue" class="q-ml-xl" style="transform: rotate(-125deg); left: -50%;" show-value :value="fuel" :thickness="0.21" :style="{color: fuelColor}">
                        <q-icon id="FuelGaugeIcon" name="fas fa-gas-pump" style="transform: rotate(125deg);" color="white"/>
                    </div>
                    <div class="responsive" id="altitudegauge" v-if="showAltitude">
                        <q-circular-progress id="Altimeter" class="q-ml-xl" style="transform: rotate(-135deg); opacity: 60%;" :value="altitudegauge" size="70px" :thickness="0.21" color="gauge" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress id="AltimeterValue" class="q-ml-xl" style="transform: rotate(-135deg); left: -50%;" show-value :value="altitude" size="70px" :thickness="0.21" color="gauge" :min="0" :max="750">
                        <altitude id="Alt">{{(altitude)}}</altitude>
                    </div>
                    <transition name="fade">
                    <div class="responsive" id="seatbelt" v-if="showSeatbelt">
                        <q-circular-progress id="SeatbeltLocation" class="q-ml-xl" style="transform: rotate(-125deg); opacity: 60%;" size="70px" :thickness="0.21" color="gauge" :min="0" :max="100"></q-circular-progress>
                        <q-circular-progress id="SeatbeltLocation" class="q-ml-xl" style="transform: rotate(-125deg); left: -40%;" show-value size="70px" :thickness="0.21" color="gauge" :min="0" :max="750">
                        <q-icon id="SeatbeltIcon" name="fas fa-user-slash" style="transform: rotate(125deg);" :value="seatbelt" size="21px" :style="{color: seatbeltColor}"/>
                    </div>
                    </transition>
                    <div class="border">
                        <div class="square" v-if="showSquare"></div>
                    </div>
                    <div class="border">
                        <div class="circle" v-if="showCircle"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
