# QB-HUD 中文版

## 您需要了解的事项：
* 游戏内打开菜单的**默认按键**是**"I"**，或者您可以输入**"/menu"**命令
* 您的设置将**始终**在**本地存储**，所以即使您删除了资源，它**仍会**保留您当前的设置。**唯一**能够成功清除本地存储的方法是点击游戏内菜单中的**"重置设置"**按钮
* 如果您想要全新的超酷菜单音效，您需要下载最新版本的 [interact-sound](https://github.com/qbcore-framework/interact-sound) <br>
（包含新的打开菜单音效、关闭菜单音效、点击菜单音效）
* 如果您在F8控制台中收到```attempt to index a nil value (global 'Lang')```错误，最快的解决方法是通过txAdminRecipe下载新版本，但如果由于您现有构建的进度而无法这样做，您将不得不逐个从GitHub下载，我不推荐这样做。通过下载recipe来节省大量时间和痛苦！

# 常见问题
##
**问：** 为什么我的边框与地图不对齐？

**答：** 大多数情况下，这通常意味着您的安全区域在GTA设置中没有设置为默认值。（设置/显示/"恢复默认值"）
##

##
**问：** 如何启用开发者模式？

**答：** 很简单！您只需要输入 /admin 并导航到菜单的最后一个部分"开发者选项"，在那里您应该看到"开发者模式"，这将使您无敌并在您的圆圈/径向中添加一个酷炫的开发者图标
##

##
**问：** 紫色圆圈/径向是做什么的？

**答：** 那是您的安全带指示器！当您的背包中有"安全带"物品并且在载具中时，它会出现。此外，当您使用"安全带"物品时，圆圈/径向将反映剩余使用次数并随时间减少。
##

# 汉化说明

本版本已完全汉化，包括：

## 界面汉化
- 所有菜单选项已翻译为中文
- 设置项描述已本地化
- 按钮和标签已汉化
- 方向指示器已汉化（北、南、东、西等）

## 系统消息汉化
- 所有通知消息已翻译
- 错误和成功提示已本地化
- 状态更新消息已汉化

## 配置文件
- 创建了中文语言文件 `locales/zh.lua`
- 更新了默认配置以支持中文界面
- 修改了manifest文件以加载中文语言

## 使用说明
1. 按 **I** 键或输入 **/menu** 打开设置菜单
2. 所有设置项现在都显示为中文
3. 设置会自动保存到本地存储
4. 如需重置设置，点击"重置设置"按钮

## 功能特性
- **状态显示**: 生命值、护甲值、饥饿值、口渴值、压力值、氧气值
- **载具信息**: 速度表、燃油表、引擎状态、氮气状态
- **导航工具**: 小地图、指南针、街道名称、方向指示
- **特殊模式**: 电影模式、开发者模式
- **音效系统**: 菜单音效、通知音效
- **自定义选项**: 帧率优化、显示设置、通知设置

## 兼容性
- 完全兼容 QBCore 框架
- 支持所有原版功能
- 保持原有性能优化

# 预览图片
### 菜单配置（这只是预览，包含的选项比这里显示的更多）
![menu](https://user-images.githubusercontent.com/********/149598723-b34bb93d-8885-4b3a-a0cc-ab68d756a449.PNG)
### 所有径向显示
![all radials](https://user-images.githubusercontent.com/********/143668930-e9475c53-284c-4054-ad9c-88aa98f76768.png)
### 死亡状态
![dead](https://user-images.githubusercontent.com/********/143668617-3f41913f-506e-4c40-bc97-99c0e02eaec6.png)
### 引擎健康度
![engine](https://user-images.githubusercontent.com/********/143668642-22269059-8220-4b78-8f24-3c3661b7e82f.png)
### 高度显示
![altitude](https://user-images.githubusercontent.com/********/143668687-89ae10b6-9acc-4d68-845d-97db67d3d6de.png)
### 降落伞
![parachute](https://user-images.githubusercontent.com/********/143668699-a9d50ee4-1168-401b-bf92-8ba80a696e6e.png)
### 武装状态
![armed](https://user-images.githubusercontent.com/********/143668646-baac9848-56e5-436b-922a-b35e50ed335f.png)
### 电影模式
![cinematic](https://user-images.githubusercontent.com/********/*********-74e90ac0-11ad-447a-b27c-1542dd10edfd.png)
### 巡航控制
![cruise](https://user-images.githubusercontent.com/********/*********-1b843009-c791-4482-807d-352b75707d42.png)
### 安全带
![harness](https://user-images.githubusercontent.com/********/*********-bd03289a-286f-4165-9447-25b16b5b0c8e.png)
### 现金
![cash](https://user-images.githubusercontent.com/********/*********-a8e2e856-94be-45c4-9751-39e71315b303.png)
### 银行
![bank](https://user-images.githubusercontent.com/********/*********-fed140e6-9043-4daa-8aba-36feac3f9b78.png)
### 氮气
![nitro](https://user-images.githubusercontent.com/********/*********-8a164eb0-aca5-4e00-a99f-56c64e4d5069.png)
### 体力
![stamina](https://user-images.githubusercontent.com/********/*********-3327c0bf-7e3b-4fe5-b6e5-da6e4054a47a.png)
### 氧气
![oxygen](https://user-images.githubusercontent.com/********/*********-d623822b-fc78-499a-baa3-a86e29504044.png)
### 无线电（过时的颜色）
![radio](https://user-images.githubusercontent.com/********/*********-eb4bb5e7-5900-4dd8-b500-5fc745a7c146.png)
